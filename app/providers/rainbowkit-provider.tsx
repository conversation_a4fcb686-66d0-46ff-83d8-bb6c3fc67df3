'use client'

import '@rainbow-me/rainbowkit/styles.css'
import {
  getDefaultConfig,
  RainbowKitProvider,
} from '@rainbow-me/rainbowkit'
import { WagmiProvider } from 'wagmi'
import {
  ,
  polygon,
  optimism,
  arbitrum,
  base,
  sepolia,
} from 'wagmi/chains'
import {
  QueryClientProvider,
  QueryClient,
} from '@tanstack/react-query'
import { useState, useEffect } from 'react'

// Create a singleton query client to prevent multiple instances
let globalQueryClient: QueryClient | undefined

function getQueryClient() {
  if (!globalQueryClient) {
    globalQueryClient = new QueryClient({
      defaultOptions: {
        queries: {
          staleTime: 60 * 1000, // 1 minute
          refetchOnWindowFocus: false,
        },
      },
    })
  }
  return globalQueryClient
}

// Initialize config only on client side to prevent indexedDB errors
let config: any = null;

function getConfig() {
  if (typeof window !== 'undefined' && !config) {
    config = getDefaultConfig({
      appName: 'Crefy Connect',
      projectId: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || 'YOUR_PROJECT_ID',
      chains: [, polygon, optimism, arbitrum, base, sepolia],
      ssr: true, // If your dApp uses server side rendering (SSR)
    });
  }
  return config;
}

export function RainbowKitProviders({
  children,
}: {
  children: React.ReactNode
}) {
  const [mounted, setMounted] = useState(false)
  const queryClient = getQueryClient()

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return <div>{children}</div>
  }

  const wagmiConfig = getConfig()

  // If config is not available (server-side), render children without providers
  if (!wagmiConfig) {
    return <div>{children}</div>
  }

  return (
    <WagmiProvider config={wagmiConfig}>
      <QueryClientProvider client={queryClient}>
        <RainbowKitProvider>
          {children}
        </RainbowKitProvider>
      </QueryClientProvider>
    </WagmiProvider>
  )
}

'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/lib/toast-context";
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useAccount } from 'wagmi';
import {
  WalletIcon,
  CheckCircleIcon,
  ExternalLinkIcon,
  InfoIcon,
  GlobeIcon,
  LinkIcon,
  PlusIcon,
  SearchIcon,
  UserPlusIcon,
  ArrowRightIcon,
  ArrowLeftIcon
} from "lucide-react";

import { useAuth } from "@/lib/auth-context";
import { apiService } from "@/lib/api";
import { DashboardLayoutWrapper } from "@/components/shared/dashboard/layout-wrapper";
import { ENSRootRegistration } from "@/components/ens/ens-root-registration";
import { SubnameAvailabilityChecker } from "@/components/ens/subname-availability-checker";
import { SubnameClaiming } from "@/components/ens/subname-claiming";
import { ApplicationSelection } from "@/components/ens/application-selection";
import { StepProgress, Step } from "@/components/ens/step-progress";
import { BreadcrumbNavigation } from "@/components/ens/breadcrumb-navigation";
import { ContextualHelp } from "@/components/ens/contextual-help";
import { ENSConnection, TransactionDetails } from "@/lib/types/ens";
import { ErrorBoundary, useErrorHandler } from "@/components/shared/error-boundary";
import { ErrorHandler, handleApiError } from "@/lib/error-handler";
import { ApplicationWithApiKey } from "@/lib/api";

type WorkflowStep = 'connect' | 'select-app' | 'register' | 'check' | 'claim';

export default function ENSIntegrationPage() {
  const { address, isConnected } = useAccount();
  const { showToast } = useToast();
  const { token } = useAuth();
  const { handleError } = useErrorHandler();
  
  const [selectedApplication, setSelectedApplication] = useState<ApplicationWithApiKey | null>(null);
  const [ensConnections, setEnsConnections] = useState<ENSConnection[]>([]);
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('connect');
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [completedSteps, setCompletedSteps] = useState<Set<WorkflowStep>>(new Set());

  // Define workflow steps
  const getWorkflowSteps = (): Step[] => {
    const hasConnectedWallet = isConnected;
    const hasSelectedApp = selectedApplication !== null;
    const hasRegisteredDomain = ensConnections.some(conn => conn.projectId === selectedApplication?.appId);
    
    return [
      {
        id: 'connect',
        title: 'Connect Wallet',
        description: 'Connect your wallet to verify ENS ownership',
        status: hasConnectedWallet ? 'completed' : currentStep === 'connect' ? 'current' : 'upcoming',
        icon: <WalletIcon className="h-5 w-5" />
      },
      {
        id: 'select-app',
        title: 'Select Application',
        description: 'Choose which application to register ENS for',
        status: !hasConnectedWallet ? 'locked' : 
                hasSelectedApp ? 'completed' : 
                currentStep === 'select-app' ? 'current' : 'upcoming',
        icon: <GlobeIcon className="h-5 w-5" />
      },
      {
        id: 'register',
        title: 'Register ENS Domain',
        description: 'Connect your ENS domain to the application',
        status: !hasSelectedApp ? 'locked' : 
                hasRegisteredDomain ? 'completed' : 
                currentStep === 'register' ? 'current' : 'upcoming',
        icon: <PlusIcon className="h-5 w-5" />
      },
      {
        id: 'check',
        title: 'Check Availability',
        description: 'Verify subname availability for users',
        status: !hasRegisteredDomain ? 'locked' : 
                currentStep === 'check' ? 'current' : 'upcoming',
        icon: <SearchIcon className="h-5 w-5" />
      },
      {
        id: 'claim',
        title: 'Claim Subnames',
        description: 'Create subnames for your users',
        status: !hasRegisteredDomain ? 'locked' : 
                currentStep === 'claim' ? 'current' : 'upcoming',
        icon: <UserPlusIcon className="h-5 w-5" />
      }
    ];
  };

  // Workflow navigation
  const navigateToStep = (stepId: string) => {
    const step = stepId as WorkflowStep;
    const steps = getWorkflowSteps();
    const targetStep = steps.find(s => s.id === stepId);
    
    if (targetStep && targetStep.status !== 'locked') {
      setCurrentStep(step);
    }
  };

  const goToNextStep = () => {
    const steps = getWorkflowSteps();
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    const nextStep = steps[currentIndex + 1];
    
    if (nextStep && nextStep.status !== 'locked') {
      setCurrentStep(nextStep.id as WorkflowStep);
    }
  };

  const goToPreviousStep = () => {
    const steps = getWorkflowSteps();
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    const previousStep = steps[currentIndex - 1];
    
    if (previousStep) {
      setCurrentStep(previousStep.id as WorkflowStep);
    }
  };

  // Handle application selection
  const handleApplicationSelect = (applicationId: string, application: ApplicationWithApiKey) => {
    setSelectedApplication(application);
    setCompletedSteps(prev => new Set([...prev, 'select-app']));

    // Auto-advance to registration step
    if (currentStep === 'select-app') {
      setCurrentStep('register');
    }

    showToast({
      type: 'success',
      title: 'Application Selected',
      description: `Selected ${application.name} for ENS integration`
    });
  };

  // Auto-advance workflow based on state changes
  useEffect(() => {
    if (isConnected && currentStep === 'connect') {
      setCompletedSteps(prev => new Set([...prev, 'connect']));
      setCurrentStep('select-app');
    }
  }, [isConnected, currentStep]);

  // Handle successful ENS root registration
  const handleENSRootSuccess = (data: any) => {
    showToast({
      type: 'success',
      title: 'ENS Root Registered Successfully! 🎉',
      description: `${data.name} is now connected to your application`
    });

    // Add to connections
    const newConnection: ENSConnection = {
      id: Date.now().toString(),
      projectId: selectedApplication?.appId || '',
      ensName: data.name,
      owner: address || '',
      connectedAt: new Date().toISOString(),
      isActive: data.isActive,
      contractAddress: data.contractAddress,
      chain: 'sepolia'
    };

    setEnsConnections(prev => [...prev, newConnection]);

    // Auto-advance to next step after successful registration
    setTimeout(() => {
      setCurrentStep('check');
      showToast({
        type: 'info',
        title: 'Next Step: Check Subname Availability',
        description: 'You can now check availability and claim subnames for your users'
      });
    }, 2000);
  };

  // Handle successful subname claim
  const handleSubnameClaimSuccess = (data: TransactionDetails) => {
    showToast({
      type: 'success',
      title: 'Subname Claimed Successfully! 🎉',
      description: `Transaction completed: ${data.hash.slice(0, 10)}...${data.hash.slice(-4)}`
    });

    // Show additional success information
    setTimeout(() => {
      showToast({
        type: 'info',
        title: 'Subname Ready',
        description: 'The subname is now available for your users to use'
      });
    }, 3000);
  };

  // Fetch applications from API
  const fetchApplications = useCallback(async () => {
    if (!token) return;

    setIsLoadingApplications(true);
    setApplicationError(null);

    try {
      const response = await apiService.getApplications(token);
      if (response.success && response.data) {
        const apps = response.data.map((app: any) => ({
          id: app.appId || app.id,
          name: app.name,
          description: app.description || 'No description available'
        }));
        setApplications(apps);

        // Auto-select first application if available and none is currently selected
        if (apps.length > 0 && !selectedApplicationId) {
          setSelectedApplicationId(apps[0].id);
        }

        // Clear any previous errors
        setApplicationError(null);
      } else {
        const errorMessage = response.error || response.message || 'Failed to fetch applications';
        const appError = handleApiError(new Error(errorMessage), {
          context: 'fetchApplications',
          token: !!token,
          responseData: response
        });

        setApplicationError(appError.userMessage || errorMessage);
        showToast({
          type: 'error',
          title: 'Failed to Load Applications',
          description: appError.userMessage || errorMessage
        });

        ErrorHandler.log(appError);
      }
    } catch (error: any) {
      console.error('Applications fetch error:', error);
      
      const appError = handleApiError(error, {
        context: 'fetchApplications',
        token: !!token,
        errorDetails: {
          message: error?.message,
          stack: error?.stack,
          name: error?.name
        }
      });

      const errorMessage = appError?.userMessage || error?.message || 'Network error occurred';
      setApplicationError(errorMessage);
      showToast({
        type: 'error',
        title: 'Network Error',
        description: errorMessage
      });

      ErrorHandler.log(appError);
    } finally {
      setIsLoadingApplications(false);
    }
  }, [token, showToast]);

  // Load existing ENS connections (mock data - replace with API call)
  useEffect(() => {
    const mockConnections: ENSConnection[] = [
      {
        id: '1',
        projectId: '1',
        ensName: 'defiprotocol.eth',
        owner: '******************************************',
        connectedAt: '2024-01-15T10:30:00Z',
        isActive: true,
        chain: 'sepolia'
      }
    ];
    setEnsConnections(mockConnections);
  }, []);

  const getApplicationName = (appId: string) => {
    return selectedApplication?.appId === appId ? selectedApplication.name : 'Unknown Application';
  };

  // Show loading skeleton on initial load
  if (isInitialLoad && isLoadingApplications) {
    return (
      <ErrorBoundary>
        <DashboardLayoutWrapper title="ENS Integration">
          <div className="flex flex-col lg:flex-row gap-6 lg:gap-8">
            {/* Sidebar Skeleton */}
            <aside className="w-full lg:w-80 bg-white/90 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-lg p-4 lg:p-6">
              <div className="space-y-6">
                <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
                {[1, 2, 3].map((i) => (
                  <div key={i} className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div className="h-20 bg-gray-100 rounded-lg animate-pulse"></div>
                  </div>
                ))}
              </div>
            </aside>

            {/* Main Content Skeleton */}
            <main className="flex-1">
              <div className="space-y-6">
                <div className="h-12 bg-gray-200 rounded-xl animate-pulse"></div>
                <div className="h-32 bg-gray-100 rounded-2xl animate-pulse"></div>
                <div className="h-48 bg-gray-100 rounded-2xl animate-pulse"></div>
              </div>
            </main>
          </div>
        </DashboardLayoutWrapper>
      </ErrorBoundary>
    );
  }

  const steps = getWorkflowSteps();
  const currentStepIndex = steps.findIndex(s => s.id === currentStep);
  const canGoNext = currentStepIndex < steps.length - 1 && steps[currentStepIndex + 1]?.status !== 'locked';
  const canGoPrevious = currentStepIndex > 0;

  const breadcrumbItems = [
    { id: 'dashboard', label: 'Dashboard' },
    { id: 'ens', label: 'ENS Integration', isActive: true },
    { id: currentStep, label: steps.find(s => s.id === currentStep)?.title || '', isActive: true }
  ];

  return (
    <ErrorBoundary>
      <DashboardLayoutWrapper title="ENS Integration">
        <div className="flex flex-col xl:flex-row gap-6 xl:gap-8">
          {/* Step Progress Sidebar */}
          <aside className="w-full xl:w-80 bg-white/90 backdrop-blur-sm border border-[#B497D6]/20 rounded-2xl shadow-lg p-4 lg:p-6 overflow-y-auto max-h-[calc(100vh-200px)] xl:sticky xl:top-0 order-2 xl:order-1">
            <div className="space-y-6">
              <div>
                <h2 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent mb-4 flex items-center gap-2">
                  <InfoIcon className="h-5 w-5 text-[#4A148C]" />
                  ENS Integration Guide
                </h2>
              </div>

              {/* Step Progress */}
              <StepProgress
                steps={steps}
                currentStepId={currentStep}
                onStepClick={navigateToStep}
              />

              {/* Contextual Help for Current Step */}
              {currentStep === 'connect' && (
                <ContextualHelp
                  title="Connect Your Wallet"
                  description="Connect your wallet to verify ENS ownership and interact with the Ethereum blockchain."
                  type="info"
                  benefits={['Secure verification', 'ENS ownership check', 'Blockchain interaction']}
                  requirements={['Compatible wallet (MetaMask, WalletConnect, etc.)', 'Ethereum network access']}
                  nextSteps={['Click Connect Wallet button', 'Approve connection in your wallet', 'Proceed to application selection']}
                />
              )}

              {currentStep === 'select-app' && (
                <ContextualHelp
                  title="Select Application"
                  description="Choose which application you want to register an ENS domain for."
                  type="info"
                  benefits={['Organized ENS management', 'Application-specific domains', 'Easy user identification']}
                  requirements={['At least one application created', 'Wallet connected']}
                  nextSteps={['Select your application from the list', 'Proceed to ENS domain registration']}
                  actions={[
                    {
                      label: 'Create Application',
                      onClick: () => window.location.href = '/dashboard/dashboard/applications',
                      variant: 'outline'
                    }
                  ]}
                />
              )}

              {currentStep === 'register' && (
                <ContextualHelp
                  title="Register ENS Domain"
                  description="Connect your existing ENS domain to enable subname creation for your users."
                  type="info"
                  benefits={['User-friendly addresses', 'Subname creation', 'Web3 identity']}
                  requirements={['Own an ENS domain (.eth)', 'Domain ownership verification', 'Selected application']}
                  nextSteps={['Enter your ENS domain name', 'Verify ownership', 'Complete registration']}
                  actions={[
                    {
                      label: 'Get ENS Domain',
                      onClick: () => window.open('https://app.ens.domains', '_blank'),
                      variant: 'outline',
                      external: true
                    }
                  ]}
                />
              )}

              {currentStep === 'check' && (
                <ContextualHelp
                  title="Check Subname Availability"
                  description="Verify if specific subnames are available for registration under your ENS domain."
                  type="info"
                  benefits={['Real-time availability', 'User validation', 'Conflict prevention']}
                  requirements={['Registered ENS domain', 'Valid subname format']}
                  nextSteps={['Enter desired subname', 'Check availability', 'Proceed to claiming if available']}
                />
              )}

              {currentStep === 'claim' && (
                <ContextualHelp
                  title="Claim Subnames"
                  description="Create subnames under your ENS domain for specific users or purposes."
                  type="info"
                  benefits={['User-specific addresses', 'Easy identification', 'Professional appearance']}
                  requirements={['Available subname', 'Gas fees for transaction', 'Wallet connection']}
                  nextSteps={['Enter subname details', 'Review transaction', 'Confirm in wallet']}
                />
              )}

              {/* Quick Stats */}
              <Card className="bg-gradient-to-br from-[#4A148C]/5 to-[#7B1FA2]/5 border-[#B497D6]/30 backdrop-blur-sm shadow-lg">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm text-[#4A148C] flex items-center gap-2 font-semibold">
                    <GlobeIcon className="h-4 w-4" />
                    Your ENS Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0 space-y-3">
                  <div className="grid grid-cols-2 gap-3 text-xs">
                    <div className="text-center p-2 bg-white/60 rounded-lg">
                      <div className="font-semibold text-[#4A148C]">{applications.length}</div>
                      <div className="text-gray-600">Applications</div>
                    </div>
                    <div className="text-center p-2 bg-white/60 rounded-lg">
                      <div className="font-semibold text-[#4A148C]">{ensConnections.length}</div>
                      <div className="text-gray-600">ENS Domains</div>
                    </div>
                  </div>
                  <div className="text-center p-2 bg-white/60 rounded-lg">
                    <div className="font-semibold text-green-600">{ensConnections.filter(c => c.isActive).length}</div>
                    <div className="text-gray-600 text-xs">Active Connections</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </aside>

          {/* Main Content Area */}
          <main className="flex-1 overflow-y-auto order-1 xl:order-2">
            <div className="space-y-6">
              {/* Breadcrumb Navigation */}
              <BreadcrumbNavigation
                items={breadcrumbItems}
                onPrevious={canGoPrevious ? goToPreviousStep : undefined}
                onNext={canGoNext ? goToNextStep : undefined}
                previousLabel="Previous Step"
                nextLabel="Next Step"
                canGoPrevious={canGoPrevious}
                canGoNext={canGoNext}
              />

              {/* Step Content */}
              {currentStep === 'connect' && (
                <Card className="p-6 bg-gradient-to-br from-[#4A148C]/5 via-[#7B1FA2]/5 to-[#4A148C]/10 border border-[#B497D6]/30 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-center space-y-6">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] rounded-full flex items-center justify-center shadow-lg">
                      <WalletIcon className="h-8 w-8 text-white" />
                    </div>

                    <div className="space-y-3">
                      <h3 className="text-2xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                        Connect Your Wallet
                      </h3>
                      <p className="text-base text-gray-600 max-w-md mx-auto leading-relaxed">
                        To use ENS functionality, you need to connect your wallet. This allows us to verify
                        ENS ownership and register domains for your applications.
                      </p>
                    </div>

                    <div className="space-y-4">
                      <div className="flex justify-center">
                        <ConnectButton />
                      </div>

                      <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-sm text-gray-500">
                        <div className="flex items-center gap-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          <span>Secure Connection</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircleIcon className="h-4 w-4 text-green-600" />
                          <span>ENS Compatible</span>
                        </div>
                      </div>
                    </div>

                    <div className="pt-4 border-t border-[#B497D6]/20">
                      <p className="text-xs text-gray-500">
                        Supported wallets: MetaMask, WalletConnect, Coinbase Wallet, and more
                      </p>
                    </div>
                  </div>
                </Card>
              )}

              {currentStep === 'select-app' && (
                <Card className="p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                      Select Application
                    </h3>
                    {!isLoadingApplications && applications.length > 0 && (
                      <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] border border-[#B497D6]/30">
                        {applications.length} app{applications.length !== 1 ? 's' : ''}
                      </Badge>
                    )}
                  </div>

                  {isLoadingApplications ? (
                    <div className="space-y-3">
                      <div className="flex items-center justify-center py-8">
                        <div className="flex items-center gap-3">
                          <div className="w-5 h-5 border-2 border-[#4A148C] border-t-transparent rounded-full animate-spin"></div>
                          <span className="text-[#4A148C] font-medium">Loading applications...</span>
                        </div>
                      </div>
                    </div>
                  ) : applicationError ? (
                    <div className="text-center py-8 space-y-4">
                      <div className="w-12 h-12 mx-auto bg-red-100 rounded-full flex items-center justify-center">
                        <ExternalLinkIcon className="h-6 w-6 text-red-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-red-800 mb-1">Failed to Load Applications</h4>
                        <p className="text-sm text-red-600 mb-4">{applicationError}</p>
                        <Button
                          onClick={fetchApplications}
                          size="sm"
                          variant="outline"
                          className="border-red-300 hover:bg-red-50 text-red-700"
                        >
                          Try Again
                        </Button>
                      </div>
                    </div>
                  ) : applications.length > 0 ? (
                    <div className="space-y-3">
                      <p className="text-sm text-gray-600 mb-4">
                        Choose which application you want to register an ENS domain for:
                      </p>
                      <div className="grid gap-3">
                        {applications.map(app => (
                          <Button
                            key={app.id}
                            variant={selectedApplicationId === app.id ? 'default' : 'outline'}
                            onClick={() => setSelectedApplicationId(app.id)}
                            className={`justify-start p-4 h-auto transition-all duration-200 ${
                              selectedApplicationId === app.id
                                ? 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white shadow-lg shadow-[#4A148C]/25'
                                : 'border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 hover:shadow-md'
                            }`}
                          >
                            <div className="flex items-center gap-3 text-left w-full">
                              <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                                selectedApplicationId === app.id
                                  ? 'bg-white/20'
                                  : 'bg-gradient-to-r from-[#4A148C] to-[#7B1FA2]'
                              }`}>
                                <GlobeIcon className="h-5 w-5 text-white" />
                              </div>
                              <div className="flex-1">
                                <div className="font-medium">{app.name}</div>
                                <div className={`text-xs ${
                                  selectedApplicationId === app.id ? 'text-white/70' : 'text-gray-500'
                                }`}>
                                  {app.description}
                                </div>
                              </div>
                              {selectedApplicationId === app.id && (
                                <CheckCircleIcon className="h-5 w-5 text-white" />
                              )}
                            </div>
                          </Button>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 space-y-4">
                      <div className="w-16 h-16 mx-auto bg-gradient-to-r from-[#4A148C]/10 to-[#7B1FA2]/10 rounded-full flex items-center justify-center">
                        <PlusIcon className="h-8 w-8 text-[#4A148C]" />
                      </div>
                      <div>
                        <h4 className="font-medium text-[#4A148C] mb-2">No Applications Found</h4>
                        <p className="text-sm text-gray-600 mb-4">
                          You need to create an application before you can register ENS domains.
                        </p>
                        <Button
                          onClick={() => window.location.href = '/dashboard/dashboard/applications'}
                          className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25"
                        >
                          <PlusIcon className="mr-2 h-4 w-4" />
                          Create Your First Application
                        </Button>
                      </div>
                    </div>
                  )}
                </Card>
              )}

              {currentStep === 'register' && selectedApplicationId && (
                <ErrorBoundary>
                  <ENSRootRegistration
                    applicationId={selectedApplicationId}
                    onSuccess={handleENSRootSuccess}
                    onError={(error) => {
                      try {
                        const appError = handleError(new Error(error), 'ENS Root Registration');
                        ErrorHandler.log(appError);
                      } catch (handlerError) {
                        console.error('Error in ENS Root Registration error handler:', handlerError);
                        showToast({
                          type: 'error',
                          title: 'ENS Registration Error',
                          description: typeof error === 'string' ? error : 'An error occurred during ENS registration'
                        });
                      }
                    }}
                  />
                </ErrorBoundary>
              )}

              {currentStep === 'check' && (
                <ErrorBoundary>
                  <SubnameAvailabilityChecker
                    ensRoot={ensConnections.find(conn => conn.projectId === selectedApplicationId)?.ensName}
                  />
                </ErrorBoundary>
              )}

              {currentStep === 'claim' && (
                <ErrorBoundary>
                  <SubnameClaiming
                    ensRoot={ensConnections.find(conn => conn.projectId === selectedApplicationId)?.ensName}
                    onSuccess={handleSubnameClaimSuccess}
                    onError={(error) => {
                      try {
                        const appError = handleError(new Error(error), 'Subname Claiming');
                        ErrorHandler.log(appError);
                      } catch (handlerError) {
                        console.error('Error in Subname Claiming error handler:', handlerError);
                        showToast({
                          type: 'error',
                          title: 'Subname Claiming Error',
                          description: typeof error === 'string' ? error : 'An error occurred during subname claiming'
                        });
                      }
                    }}
                    showAccountAbstraction={true}
                  />
                </ErrorBoundary>
              )}

              {/* Connected ENS Names Dashboard */}
              {ensConnections.length > 0 && (
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-semibold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent">
                      Connected ENS Names
                    </h2>
                    <div className="flex items-center gap-4">
                      <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] border border-[#B497D6]/30">
                        {ensConnections.length} domain{ensConnections.length !== 1 ? 's' : ''}
                      </Badge>
                      <Button
                        onClick={() => setCurrentStep('register')}
                        size="sm"
                        className="bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white"
                      >
                        <PlusIcon className="mr-2 h-4 w-4" />
                        Add Domain
                      </Button>
                    </div>
                  </div>

                  {/* Responsive ENS Connections Grid */}
                  <div className="grid gap-4 sm:gap-6 sm:grid-cols-1 md:grid-cols-2 xl:grid-cols-3">
                    {ensConnections.map((connection, index) => (
                      <Card key={index} className="group p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
                        <div className="space-y-3 sm:space-y-4">
                          {/* Responsive Header */}
                          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#4A148C] to-[#7B1FA2] rounded-xl flex items-center justify-center shadow-lg flex-shrink-0">
                                <GlobeIcon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                              </div>
                              <div className="min-w-0 flex-1">
                                <h3 className="font-bold text-base sm:text-lg text-[#4A148C] group-hover:text-[#7B1FA2] transition-colors truncate">
                                  {connection.ensName}
                                </h3>
                                <p className="text-xs sm:text-sm text-gray-600 truncate">
                                  {getApplicationName(connection.projectId)}
                                </p>
                              </div>
                            </div>

                            <Badge className={`${
                              connection.isActive
                                ? "bg-green-100 text-green-800 border-green-200"
                                : "bg-gray-100 text-gray-800 border-gray-200"
                            } border`}>
                              <div className={`w-2 h-2 rounded-full mr-1 ${
                                connection.isActive ? 'bg-green-600' : 'bg-gray-600'
                              }`}></div>
                              {connection.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>

                          {/* Details */}
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                              <span className="text-gray-600 font-medium">Owner:</span>
                              <span className="font-mono bg-white px-2 py-1 rounded border text-[#4A148C]">
                                {connection.owner.slice(0, 6)}...{connection.owner.slice(-4)}
                              </span>
                            </div>

                            <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                              <span className="text-gray-600 font-medium">Connected:</span>
                              <span className="text-gray-800">
                                {new Date(connection.connectedAt).toLocaleDateString()}
                              </span>
                            </div>

                            <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                              <span className="text-gray-600 font-medium">Network:</span>
                              <Badge variant="secondary" className="bg-[#B497D6]/20 text-[#4A148C] border border-[#B497D6]/30 text-xs">
                                {connection.chain.charAt(0).toUpperCase() + connection.chain.slice(1)}
                              </Badge>
                            </div>
                          </div>

                          {/* Responsive Actions */}
                          <div className="pt-3 sm:pt-4 border-t border-gray-200 flex flex-col sm:flex-row gap-2">
                            <Button
                              onClick={() => setCurrentStep('check')}
                              size="sm"
                              variant="outline"
                              className="flex-1 min-h-[36px] border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 text-xs"
                            >
                              <SearchIcon className="mr-1 h-3 w-3" />
                              <span className="hidden sm:inline">Check Subnames</span>
                              <span className="sm:hidden">Check</span>
                            </Button>
                            <Button
                              onClick={() => setCurrentStep('claim')}
                              size="sm"
                              variant="outline"
                              className="flex-1 min-h-[36px] border-[#B497D6]/30 hover:border-[#4A148C] hover:bg-gradient-to-r hover:from-[#4A148C]/10 hover:to-[#7B1FA2]/10 text-xs"
                            >
                              <UserPlusIcon className="mr-1 h-3 w-3" />
                              <span className="hidden sm:inline">Claim Subname</span>
                              <span className="sm:hidden">Claim</span>
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>

                  {/* Responsive Quick Stats */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mt-6">
                    <Card className="p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200">
                      <div className="flex items-center gap-2 sm:gap-3">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <CheckCircleIcon className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                        </div>
                        <div className="min-w-0">
                          <p className="text-xs sm:text-sm text-green-700 font-medium">Active Domains</p>
                          <p className="text-lg sm:text-xl font-bold text-green-800">
                            {ensConnections.filter(c => c.isActive).length}
                          </p>
                        </div>
                      </div>
                    </Card>

                    <Card className="p-3 sm:p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200">
                      <div className="flex items-center gap-2 sm:gap-3">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <GlobeIcon className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                        </div>
                        <div className="min-w-0">
                          <p className="text-xs sm:text-sm text-blue-700 font-medium">Total Domains</p>
                          <p className="text-lg sm:text-xl font-bold text-blue-800">{ensConnections.length}</p>
                        </div>
                      </div>
                    </Card>

                    <Card className="p-3 sm:p-4 bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 sm:col-span-2 lg:col-span-1">
                      <div className="flex items-center gap-2 sm:gap-3">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <LinkIcon className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" />
                        </div>
                        <div className="min-w-0">
                          <p className="text-xs sm:text-sm text-purple-700 font-medium">Applications</p>
                          <p className="text-lg sm:text-xl font-bold text-purple-800">
                            {new Set(ensConnections.map(c => c.projectId)).size}
                          </p>
                        </div>
                      </div>
                    </Card>
                  </div>
                </div>
              )}
            </div>
          </main>
        </div>
      </DashboardLayoutWrapper>
    </ErrorBoundary>
  );
}

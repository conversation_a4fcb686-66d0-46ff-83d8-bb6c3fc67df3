'use client';

import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/toast-context";
import { useAccount } from 'wagmi';
import { 
  CheckCircleIcon, 
  AlertCircleIcon,
  SearchIcon,
  GlobeIcon,
  LoaderIcon,
  InfoIcon
} from "lucide-react";
import { ENSErrorHandler } from "@/lib/ens-error-handler";
import { normalize } from 'viem/ens';

export interface ENSNameEntryProps {
  onENSVerified: (ensName: string, contractAddress: string) => void;
  onError?: (error: string) => void;
  className?: string;
}

interface ENSValidationResult {
  isValid: boolean;
  isOwned: boolean;
  owner?: string;
  contractAddress?: string;
  error?: string;
}

export function ENSNameEntry({ 
  onENSVerified, 
  onError, 
  className = "" 
}: ENSNameEntryProps) {
  const { showToast } = useToast();
  const { address, isConnected } = useAccount();
  
  const [ensName, setEnsName] = useState<string>('');
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [validationResult, setValidationResult] = useState<ENSValidationResult | null>(null);
  const [inputError, setInputError] = useState<string>('');

  // Reset validation when ENS name changes
  useEffect(() => {
    if (ensName !== validationResult?.ensName) {
      setValidationResult(null);
      setInputError('');
    }
  }, [ensName, validationResult?.ensName]);

  // Validate ENS name format
  const validateENSFormat = (name: string): boolean => {
    if (!name) return false;
    
    // Basic ENS name validation - should end with .eth or other TLD
    const ensPattern = /^[a-z0-9-]+\.[a-z]{2,}$/i;
    return ensPattern.test(name.trim());
  };

  // Check ENS ownership using viem
  const checkENSOwnership = async (ensName: string): Promise<ENSValidationResult> => {
    try {
      // Normalize the ENS name
      const normalizedName = normalize(ensName.trim().toLowerCase());
      
      // For demo purposes, we'll simulate ENS ownership check
      // In a real implementation, you would use viem's ENS functions:
      // import { getEnsAddress, getEnsName } from 'viem/ens'
      // const owner = await getEnsAddress(client, { name: normalizedName })
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock validation - in real implementation, check against blockchain
      const mockOwner = address; // Simulate that connected wallet owns the ENS
      const mockContractAddress = "******************************************"; // Mock contract address
      
      if (!mockOwner) {
        return {
          isValid: true,
          isOwned: false,
          error: 'ENS name is not owned by any address'
        };
      }
      
      const isOwned = mockOwner.toLowerCase() === address?.toLowerCase();
      
      return {
        isValid: true,
        isOwned,
        owner: mockOwner,
        contractAddress: isOwned ? mockContractAddress : undefined,
        error: isOwned ? undefined : 'You do not own this ENS name'
      };
      
    } catch (error) {
      console.error('ENS ownership check failed:', error);
      return {
        isValid: false,
        isOwned: false,
        error: 'Failed to verify ENS ownership'
      };
    }
  };

  const handleValidateENS = async () => {
    if (!isConnected) {
      setInputError('Please connect your wallet first');
      return;
    }

    if (!ensName.trim()) {
      setInputError('Please enter an ENS name');
      return;
    }

    if (!validateENSFormat(ensName)) {
      setInputError('Please enter a valid ENS name (e.g., myname.eth)');
      return;
    }

    setIsValidating(true);
    setInputError('');

    try {
      const result = await checkENSOwnership(ensName);
      setValidationResult({ ...result, ensName });

      if (result.isValid && result.isOwned && result.contractAddress) {
        showToast({
          type: 'success',
          title: 'ENS Ownership Verified',
          description: `You own ${ensName} and can proceed with linking`
        });
        onENSVerified(ensName, result.contractAddress);
      } else if (result.error) {
        setInputError(result.error);
        onError?.(result.error);
        showToast({
          type: 'error',
          title: 'Verification Failed',
          description: result.error
        });
      }
    } catch (error) {
      const ensError = ENSErrorHandler.handleError(error, 'ENS Ownership Verification');
      setInputError(ensError.userMessage);
      onError?.(ensError.userMessage);
      ENSErrorHandler.showErrorToast(ensError);
    } finally {
      setIsValidating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isValidating && ensName.trim()) {
      handleValidateENS();
    }
  };

  const getValidationIcon = () => {
    if (isValidating) {
      return <LoaderIcon className="h-5 w-5 animate-spin text-[#4A148C]" />;
    }
    
    if (validationResult?.isValid && validationResult?.isOwned) {
      return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
    }
    
    if (validationResult && (!validationResult.isValid || !validationResult.isOwned)) {
      return <AlertCircleIcon className="h-5 w-5 text-red-600" />;
    }
    
    return <SearchIcon className="h-5 w-5 text-gray-400" />;
  };

  const getValidationMessage = () => {
    if (isValidating) {
      return "Verifying ENS ownership...";
    }
    
    if (validationResult?.isValid && validationResult?.isOwned) {
      return `✅ You own ${ensName}`;
    }
    
    if (validationResult && validationResult.error) {
      return `❌ ${validationResult.error}`;
    }
    
    return null;
  };

  return (
    <Card className={`p-4 sm:p-6 bg-white/80 backdrop-blur-sm border border-[#B497D6]/20 shadow-lg rounded-2xl hover:shadow-xl transition-all duration-300 ${className}`}>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg sm:text-xl font-bold bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] bg-clip-text text-transparent flex items-center gap-2">
          <GlobeIcon className="h-5 w-5 sm:h-6 sm:w-6 text-[#4A148C]" />
          Enter Your ENS Name
        </CardTitle>
        <p className="text-sm text-gray-600 mt-2 leading-relaxed">
          Enter the ENS name you own and want to link to your application. We'll verify that your connected wallet owns this ENS name.
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Info Banner */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start gap-3">
            <InfoIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">ENS Ownership Required</p>
              <p>You must own the ENS name with your connected wallet address to proceed. We'll verify ownership on-chain before allowing you to link it to your application.</p>
            </div>
          </div>
        </div>

        {/* ENS Name Input */}
        <div className="space-y-2">
          <Label htmlFor="ens-name" className="text-sm font-medium text-gray-700">
            ENS Name
          </Label>
          <div className="relative">
            <Input
              id="ens-name"
              type="text"
              placeholder="myname.eth"
              value={ensName}
              onChange={(e) => setEnsName(e.target.value)}
              onKeyPress={handleKeyPress}
              className={`pr-12 ${inputError ? 'border-red-300 focus:border-red-500' : 'border-[#B497D6]/30 focus:border-[#4A148C]'}`}
              disabled={isValidating}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              {getValidationIcon()}
            </div>
          </div>
          
          {/* Error Message */}
          {inputError && (
            <p className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircleIcon className="h-4 w-4" />
              {inputError}
            </p>
          )}
          
          {/* Validation Message */}
          {getValidationMessage() && (
            <p className={`text-sm ${validationResult?.isValid && validationResult?.isOwned ? 'text-green-600' : 'text-red-600'}`}>
              {getValidationMessage()}
            </p>
          )}
        </div>

        {/* Connected Wallet Info */}
        {isConnected && address && (
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-xs text-gray-600 mb-1">Connected Wallet:</p>
            <code className="text-xs bg-white px-2 py-1 rounded border">
              {address.slice(0, 6)}...{address.slice(-4)}
            </code>
          </div>
        )}

        {/* Verify Button */}
        <Button
          onClick={handleValidateENS}
          disabled={!ensName.trim() || isValidating || !isConnected}
          className="w-full bg-gradient-to-r from-[#4A148C] to-[#7B1FA2] text-white hover:shadow-lg hover:shadow-[#4A148C]/25 hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
        >
          {isValidating ? (
            <>
              <LoaderIcon className="h-4 w-4 mr-2 animate-spin" />
              Verifying Ownership...
            </>
          ) : (
            <>
              <SearchIcon className="h-4 w-4 mr-2" />
              Verify ENS Ownership
            </>
          )}
        </Button>

        {/* Help Text */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• ENS names should end with .eth or another valid TLD</p>
          <p>• Your connected wallet must be the owner of the ENS name</p>
          <p>• Verification is done on-chain for security</p>
        </div>
      </CardContent>
    </Card>
  );
}

# ENS Integration Guide - Crefy Connect Platform

## Overview

The ENS (Ethereum Name Service) integration allows developers to link their ENS domains to applications on the Crefy Connect platform, enabling decentralized identity and user-friendly addresses for their users.

## 4-Step Workflow Process

### Step 1: Connect Wallet
- **Purpose**: Establish wallet connection for ENS ownership verification
- **Technology**: RainbowKit integration
- **User Action**: Click "Connect Wallet" button
- **Validation**: Wallet must be successfully connected
- **Auto-advancement**: Automatically proceeds to Step 2 when wallet is connected

### Step 2: Select Application
- **Purpose**: Choose which application to link the ENS domain to
- **Data Source**: `/apps` API endpoint
- **Component**: `ApplicationSelection`
- **User Action**: Select from list of existing applications
- **Validation**: Must select an application
- **Auto-advancement**: Automatically proceeds to Step 3 when application is selected

### Step 3: Enter ENS Name
- **Purpose**: Enter and verify ownership of ENS domain
- **Component**: `ENSNameEntry`
- **Technology**: Real blockchain verification using viem/wagmi
- **User Action**: 
  1. Enter ENS name (e.g., "myname.eth")
  2. Click "Verify ENS Ownership"
- **Validation**: 
  - ENS name format validation
  - Real-time blockchain ownership verification
  - Must own the ENS name with connected wallet
- **Auto-advancement**: Automatically proceeds to Step 4 when ownership is verified

### Step 4: Link ENS to Application
- **Purpose**: Register/link the verified ENS name to the selected application
- **Component**: `ENSRootRegistration` (with prefilled data)
- **API Endpoint**: `POST /ens/roots`
- **User Action**: Click "Link ENS to Application"
- **Data Sent**:
  ```json
  {
    "ens_name": "verified-ens-name.eth",
    "contractAddress": "0x...",
    "chain": "sepolia",
    "isActive": true
  }
  ```
- **Authentication**: Uses `appId` as `x-api-key` header

## Technical Implementation

### Core Components

#### 1. Main ENS Page (`app/dashboard/dashboard/ens/page.tsx`)
- **Responsibility**: Orchestrates the 4-step workflow
- **State Management**: 
  - `currentStep`: Current workflow step
  - `completedSteps`: Set of completed steps
  - `selectedApplication`: Selected application data
  - `enteredENSName`: Verified ENS name
  - `isENSOwnershipVerified`: Ownership verification status
  - `ensContractAddress`: Contract address from verification

#### 2. ENS Name Entry (`components/ens/ens-name-entry.tsx`)
- **Responsibility**: ENS name input and ownership verification
- **Features**:
  - Real-time format validation
  - Blockchain-based ownership verification
  - User-friendly error messages
  - Loading states and progress indicators

#### 3. ENS Root Registration (`components/ens/ens-root-registration.tsx`)
- **Responsibility**: Link verified ENS to application
- **Features**:
  - Accepts prefilled ENS name and contract address
  - Disabled inputs when using prefilled data
  - API integration with proper error handling
  - Progress indicators and success feedback

#### 4. ENS Verification Service (`lib/services/ens-verification.ts`)
- **Responsibility**: Real blockchain ENS verification
- **Features**:
  - Uses viem/wagmi for blockchain interaction
  - Supports mainnet and testnet
  - ENS name format validation
  - Ownership verification
  - Reverse resolution (address to ENS)

### API Integration

#### ENS Endpoints (`lib/api.ts`)

1. **GET /ens/roots**
   - Retrieve existing ENS roots
   - Authentication: Bearer token

2. **POST /ens/roots**
   - Register new ENS root
   - Authentication: Bearer token + appId as x-api-key header
   - Payload: ENSRootRegistrationRequest

3. **GET /ens/subnames/check-availability**
   - Check subname availability
   - Authentication: Bearer token + appId as x-api-key header

4. **POST /ens/subnames/claim**
   - Claim subname
   - Authentication: Bearer token + appId as x-api-key header

### Contract Integration

#### Contract ABIs (`lib/contracts/`)
- **Factory Contract**: ENS subname registration factory
- **L1 Subname Registrar**: Subname registration logic
- **Name Wrapper**: ENS name wrapping functionality

#### Contract Configuration
```typescript
export const getContractConfig = (network: SupportedNetwork) => {
  return {
    factory: FACTORY_CONTRACT_CONFIG[network],
    nameWrapper: NAMEWRAPPER_CONTRACT_CONFIG[network],
    network: CONTRACT_NETWORKS[network]
  };
};
```

## Navigation and State Management

### Forward Navigation
- **Automatic**: Steps auto-advance when requirements are met
- **Manual**: "Next Step" button when available
- **Validation**: Cannot proceed until current step is completed

### Backward Navigation
- **Previous Button**: Available on all steps except first
- **State Reset**: Automatically resets dependent state when going backwards
- **Wallet Disconnection**: Properly handled with state cleanup

### State Reset Logic
```typescript
// Going back to connect step - reset all state
if (previousStep.id === 'connect') {
  setSelectedApplication(null);
  setEnteredENSName('');
  setIsENSOwnershipVerified(false);
  setEnsContractAddress('');
  setCompletedSteps(new Set());
}
```

## Error Handling

### ENS Error Handler (`lib/ens-error-handler.ts`)
- **Centralized Error Processing**: Consistent error handling across components
- **User-Friendly Messages**: Technical errors converted to user-friendly text
- **Recovery Suggestions**: Actionable guidance for error resolution
- **Toast Notifications**: Visual feedback for all error states

### Error Categories
1. **Validation Errors**: Format, ownership, authentication
2. **Network Errors**: API failures, blockchain connectivity
3. **User Errors**: Invalid input, insufficient permissions
4. **System Errors**: Unexpected failures with fallback handling

## Design System

### Purple Gradient Theme
- **Primary**: #4A148C to #7B1FA2
- **Background**: White with backdrop-blur effects
- **Borders**: [#B497D6]/20 opacity
- **Hover States**: Enhanced shadows and scaling

### Responsive Design
- **Mobile**: Touch-friendly interfaces, stacked layouts
- **Tablet**: Optimized spacing and component sizing
- **Desktop**: Full feature set with side-by-side layouts

## Testing and Development

### Development Environment
- **Network**: Sepolia Testnet for ENS verification
- **Test Account**: <EMAIL> / 123456
- **Mock Data**: Fallback for development when blockchain unavailable

### Testing Checklist
1. **Wallet Connection**: RainbowKit integration
2. **Application Selection**: API endpoint integration
3. **ENS Verification**: Real blockchain verification
4. **API Integration**: All endpoints with proper authentication
5. **Navigation**: Forward/backward with state management
6. **Error Handling**: All error scenarios
7. **Responsive Design**: All device sizes

## Troubleshooting

### Specific Issue Resolutions

#### 1. Step 4 (Link ENS to Application) Completion Issue

**Problem**: Users cannot complete the final step of linking their verified ENS name to the selected application.

**Root Cause Analysis**:
- Check if `handleRegister` function is properly called
- Verify API payload structure matches backend expectations
- Ensure `appId` is correctly passed as `x-api-key` header

**Solution Steps**:
1. **Verify API Call Structure**:
   ```typescript
   const registrationData: ENSRootRegistrationRequest = {
     ens_name: formData.ensName,
     contractAddress: formData.contractAddress,
     chain: formData.chain,
     isActive: true
   };

   const response = await apiService.registerENSRoot(
     registrationData,
     token,
     applicationId // This becomes x-api-key header
   );
   ```

2. **Debug API Response**:
   - Check browser Network tab for API call details
   - Verify response status and error messages
   - Ensure backend endpoint is accessible

3. **Validate Authentication**:
   - Confirm user is logged in with valid token
   - Verify applicationId is correctly passed
   - Check x-api-key header in request

#### 2. Backward Navigation to Step 1

**Problem**: Users cannot navigate back to Step 1 (Connect Wallet) using the "Previous" button.

**Solution**: Enhanced state management with proper cleanup:
```typescript
const goToPreviousStep = () => {
  // ... existing logic ...

  if (previousStep.id === 'connect') {
    // Reset all state when going back to connect step
    setSelectedApplication(null);
    setEnteredENSName('');
    setIsENSOwnershipVerified(false);
    setEnsContractAddress('');
    setCompletedSteps(new Set());
  }
};
```

#### 3. ENS Name Verification Implementation

**Current Implementation**: Real blockchain-based verification using viem/wagmi:
```typescript
// Real ENS verification service
const result = await ENSVerificationService.verifyOwnership(
  ensName,
  address as Address,
  1 // Use mainnet for ENS verification
);
```

**Features**:
- Uses viem public clients for blockchain interaction
- Supports mainnet and Sepolia testnet
- Real-time ownership verification
- Proper error handling and user feedback

#### 4. Backend Integration Status

**Current State**: All API endpoints are properly implemented:
- ✅ POST `/ens/roots` - ENS root registration
- ✅ GET `/ens/roots` - Retrieve ENS roots
- ✅ GET `/ens/subnames/check-availability` - Check availability
- ✅ POST `/ens/subnames/claim` - Claim subnames

**Authentication**: All endpoints use proper authentication:
- Bearer token for user authentication
- appId as x-api-key header for application context

### Common Issues

1. **ENS Verification Fails**
   - Check wallet connection
   - Verify ENS name ownership on blockchain
   - Ensure using correct network (mainnet for ENS)

2. **API Calls Fail**
   - Verify authentication token
   - Check appId in x-api-key header
   - Validate request payload format

3. **Navigation Issues**
   - Check step completion status
   - Verify state management
   - Review validation logic

4. **Contract Interaction Fails**
   - Verify contract addresses
   - Check network configuration
   - Ensure proper ABI imports

### Debug Tools
- Browser console for error logs
- Network tab for API call inspection
- React DevTools for state inspection
- Wallet developer tools for transaction details

## Production Deployment

### Requirements
1. **Environment Variables**: API endpoints, contract addresses
2. **Network Configuration**: Mainnet for production ENS
3. **Error Monitoring**: Sentry or similar for error tracking
4. **Performance Monitoring**: API response times, user flows

### Security Considerations
1. **API Authentication**: Secure token handling
2. **Wallet Security**: No private key storage
3. **Input Validation**: Server-side validation for all inputs
4. **Rate Limiting**: Prevent API abuse

## Future Enhancements

### Planned Features
1. **Multi-chain Support**: Support for other ENS-compatible chains
2. **Batch Operations**: Multiple ENS registrations
3. **Advanced Analytics**: Usage metrics and insights
4. **Custom Resolvers**: Support for custom ENS resolvers

### Integration Opportunities
1. **IPFS Integration**: Decentralized content hosting
2. **Social Features**: ENS-based social profiles
3. **Payment Integration**: ENS-based payment addresses
4. **Identity Verification**: Enhanced KYC with ENS

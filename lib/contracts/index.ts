// Contract ABIs and Configuration Index
// Centralized exports for all ENS-related smart contracts

// Factory Contract
export {
  FACTORY_CONTRACT_ADDRESS,
  FACTORY_CONTRACT_ABI,
  FACTORY_CONTRACT_CONFIG,
  type FactoryContractAddress,
  type FactoryContractABI,
  type SupportedFactory<PERSON>hain
} from './factory-contract';

// L1 Subname Registrar Contract
export {
  L1SUBNAME_REGISTRAR_CONTRACT_ADDRESS,
  L1SUBNAME_REGISTRAR_CONTRACT_ABI,
  type L1SubnameRegistrarContractAddress,
  type L1SubnameRegistrarContractABI
} from './l1-subname-registrar';

// Name Wrapper Contract
export {
  NAMEWRAPPER_CONTRACT_ADDRESS,
  NAMEWRAPPER_CONTRACT_ABI,
  NAMEWRAPPER_CONTRACT_CONFIG,
  type NameWrapperContractAddress,
  type NameWrapperContractABI,
  type SupportedNameWrapper<PERSON>hain
} from './name-wrapper';

// Contract utilities and helpers
export const CONTRACT_NETWORKS = {
  sepolia: {
    chainId: 11155111,
    name: 'Sepolia Testnet',
    rpcUrl: 'https://sepolia.infura.io/v3/',
    blockExplorer: 'https://sepolia.etherscan.io'
  }
} as const;

export type SupportedNetwork = keyof typeof CONTRACT_NETWORKS;

// Contract interaction helpers
export const getContractConfig = (network: SupportedNetwork) => {
  return {
    factory: FACTORY_CONTRACT_CONFIG[network],
    nameWrapper: NAMEWRAPPER_CONTRACT_CONFIG[network],
    network: CONTRACT_NETWORKS[network]
  };
};

// Contract addresses for easy access
export const CONTRACT_ADDRESSES = {
  sepolia: {
    factory: FACTORY_CONTRACT_ADDRESS,
    nameWrapper: NAMEWRAPPER_CONTRACT_ADDRESS
  }
} as const;
